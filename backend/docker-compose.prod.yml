services:
  api:
    deploy:
      resources:
        limits:
          cpus: "14"
          memory: 48G
        reservations:
          cpus: "8"
          memory: 32G

  worker:
    command: python -m dramatiq --processes 20 --threads 16 run_agent_background
    deploy:
      resources:
        limits:
          cpus: "14"
          memory: 48G
        reservations:
          cpus: "8"
          memory: 32G

  redis:
    deploy:
      resources:
        limits:
          cpus: "2"
          memory: 12G
        reservations:
          cpus: "1"
          memory: 8G

  rabbitmq:
    deploy:
      resources:
        limits:
          cpus: "2"
          memory: 12G
        reservations:
          cpus: "1"
          memory: 8G
